"""
榜单模型
"""
from datetime import datetime
from enum import Enum

from sqlalchemy import (
    Boolean, Column, DateTime, ForeignKey, Integer, String, Text, Time
)
from sqlalchemy.orm import relationship

from app.core.database import Base


class RankingStatus(str, Enum):
    """榜单状态枚举"""
    NOT_STARTED = "not_started"  # 未开始
    IN_PROGRESS = "in_progress"  # 进行中
    FINISHED = "finished"        # 已结束


class RankingType(str, Enum):
    """榜单类型枚举"""
    FIVE_PERSON = "5_person"   # 5人榜单
    TEN_PERSON = "10_person"   # 10人榜单


class Ranking(Base):
    """榜单表"""
    __tablename__ = "rankings"

    id = Column(Integer, primary_key=True, index=True)
    
    # 基础信息
    name = Column(String(200), nullable=False, comment="榜单名称")
    period = Column(Integer, nullable=False, comment="期数")
    ranking_type = Column(String(20), nullable=False, comment="榜单类型")
    
    # 时间信息
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=False, comment="结束时间")
    
    # 配置信息
    team_size_limit = Column(Integer, nullable=False, comment="组队人数限制")
    total_participants = Column(Integer, default=0, nullable=False, comment="总参与人数")
    
    # 状态信息
    status = Column(String(20), default=RankingStatus.NOT_STARTED, nullable=False, comment="榜单状态")
    
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")
    
    # 关系
    details = relationship("RankingDetail", back_populates="ranking", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Ranking(id={self.id}, name='{self.name}', period={self.period})>"


class RankingDetail(Base):
    """榜单明细表"""
    __tablename__ = "ranking_details"

    id = Column(Integer, primary_key=True, index=True)

    # 关联信息
    ranking_id = Column(Integer, ForeignKey("rankings.id"), nullable=False, comment="榜单ID")

    # 排名信息
    rank_start = Column(Integer, nullable=False, comment="排名开始")
    rank_end = Column(Integer, nullable=False, comment="排名结束")

    # 时间信息
    completion_time = Column(Time, nullable=False, comment="完成时间(分秒)")
    completion_seconds = Column(Integer, nullable=False, comment="完成时间(总秒数)")

    # 参与信息
    participant_count = Column(Integer, nullable=False, comment="当前时间区间参与人数")
    team_info = Column(Text, nullable=True, comment="队伍信息(JSON格式)")
    team_name = Column(String(100), nullable=True, comment="队伍名称")

    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False, comment="更新时间")

    # 关系
    ranking = relationship("Ranking", back_populates="details")

    def __repr__(self):
        return f"<RankingDetail(id={self.id}, ranking_id={self.ranking_id}, rank={self.rank_start}-{self.rank_end})>"
