<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="db8b5f25-d3f2-4255-b8a3-6d475ece52d5" name="Changes" comment="refactor(api): 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由">
      <change afterPath="$PROJECT_DIR$/alembic/versions/003_add_team_name_to_ranking_details.py" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/docs/alembic.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Dockerfile" beforeDir="false" afterPath="$PROJECT_DIR$/Dockerfile" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/api/v1/endpoints/rankings.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/api/v1/endpoints/rankings.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/app/schemas/ranking.py" beforeDir="false" afterPath="$PROJECT_DIR$/app/schemas/ranking.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/DATABASE_SQL.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/DATABASE_SQL.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docs/database_design.md" beforeDir="false" afterPath="$PROJECT_DIR$/docs/database_design.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/nginx/conf.d/yysls.conf.template" beforeDir="false" afterPath="$PROJECT_DIR$/nginx/conf.d/yysls.conf.template" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pyproject.toml" beforeDir="false" afterPath="$PROJECT_DIR$/pyproject.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/generate_api_docs.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/generate_api_docs.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/scripts/generate_sql_scripts.py" beforeDir="false" afterPath="$PROJECT_DIR$/scripts/generate_sql_scripts.py" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/mysql_create_tables.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/mysql_create_tables.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/tests/test_services/test_user_service.py" beforeDir="false" afterPath="$PROJECT_DIR$/tests/test_services/test_user_service.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$PROJECT_DIR$/../../../go/server/go1.22.2" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="30YMmWunNTDkWlTPetish0WzfN0" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.automatic.dependencies.download": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "master",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "last_opened_file_path": "D:/h5_code/yysls/yysls-ranking-backend",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "http.proxy"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\h5_code\yysls\yysls-ranking-backend\ssh" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="init: 基础结构" />
    <MESSAGE value="feat: 添加了 JWT 认证和权限控制&#10;- 完善了服务层的通用方法&#10;&#10;新增功能：&#10;- 用户管理、权限控制、搜索功能&#10;- 赞助商管理、状态切换、排序功能&#10;- 系统配置管理、批量更新、缓存刷新&#10;- 内容管理、公告列表、播报消息&#10;&#10;优化服务层：&#10;- 为 BaseService 添加了 get_multi_with_total 方法&#10;- 为各服务类添加了 search_* 方法支持搜索功能&#10;- 完善了错误处理和日志记录&#10;-统一了服务层的方法签名和返回格式" />
    <MESSAGE value="fix: 重构数据库操作并优化榜单相关逻辑&#10;&#10;-移除 async 从数据库操作中，使用同步方式替代&#10;- 更新数据模型和 schema 以适应新的业务需求&#10;- 优化榜单创建、更新和查询逻辑&#10;-调整用户认证和积分管理相关函数&#10;- 更新数据库迁移脚本以匹配新的数据模型" />
    <MESSAGE value="refactor(docker): 调整环境变量加载顺序&#10;&#10;-将 .env.prod 文件的加载移到依赖服务之前&#10;- 优化环境变量配置，提高可维护性" />
    <MESSAGE value="build(Dockerfile): 使用国内源加速 apt&#10;&#10;- 修改 /etc/apt/sources.list 文件，将 Debian 官方源替换为清华大学镜像源&#10;- 更新 apt 软件包列表并安装所需的系统依赖" />
    <MESSAGE value="build(Dockerfile): 更新系统依赖安装方式&#10;&#10;- 适配 slim 镜像，使用精简的 sources.list&#10;- 添加 bookworm-updates 和 security 仓库，确保系统安全更新&#10;- 优化 RUN 指令，减少镜像层数" />
    <MESSAGE value="build(Dockerfile): 配置国内 pip 源并安装 Python 依赖&#10;&#10;- 使用清华大学源作为主源，阿里云源作为备用源&#10;- 设置可信主机以避免 SSL 警告&#10;- 在安装依赖前先升级 pip" />
    <MESSAGE value="build:配置 pip 使用国内源&#10;&#10;- 添加 pip.conf 文件，配置国内 PyPI 镜像源&#10;- 主要使用清华大学源，备用阿里云、豆瓣、中科大源&#10;- 设置信任的主机和超时参数&#10;-禁用缓存目录，适合 Docker 环境&#10;- 注释掉不检查证书的配置项" />
    <MESSAGE value="feat: 添加 Nginx 配置并优化项目结构&#10;&#10;- 新增 Nginx 主配置文件和站点配置模板&#10;- 添加开发环境配置示例&#10;- 优化 Docker Compose 配置&#10;- 更新 .gitignore 文件，排除敏感信息&#10;- 添加时区设置和日志卷挂载" />
    <MESSAGE value="perf(mysql): 优化 MySQL 配置以适应低内存环境&#10;&#10;- 调整 InnoDB 缓冲池和日志文件大小- 移除已废弃的查询缓存配置&#10;- 适应 MySQL 8.0 及以上版本" />
    <MESSAGE value="chore(mysql): 调整 MySQL 日志配置&#10;&#10;- 将错误日志输出改为 stderr&#10;- 修改慢查询日志文件路径为 /var/lib/mysql/slow.log- 移除多余的空行" />
    <MESSAGE value="refactor: 优化 MySQL配置文件设置&#10;&#10;- 移除了不必要的注释和冗余配置&#10;- 调整了连接配置和缓冲区大小，以适应更高负载&#10;- 更新了字符集和排序规则配置&#10;-简化了日志和安全配置&#10;- 添加了 binlog 配置，用于数据复制和恢复- 设置了更严格的 SQL 模式" />
    <MESSAGE value="build(deps): 添加 email-validator以支持 Pydantic EmailStr- 在 requirements.txt 中添加 email-validator==2.1.0&#10;- 此依赖用于支持 Pydantic 库中的 EmailStr 类型验证" />
    <MESSAGE value="feat: 重构赞助商相关功能&#10;&#10;- 简化了赞助商模型，移除了不必要的字段&#10;- 重新设计了赞助商列表和搜索逻辑，支持分页和排序&#10;- 添加了更新赞助商排序顺序的功能&#10;- 修改了激活状态切换逻辑&#10;-移除了异步数据库操作，改为同步操作&#10;- 更新了相关的数据库设计文档和SQL脚本" />
    <MESSAGE value="feat: 优化用户模型和接口&#10;&#10;- 在 User 模型中添加了年龄、性别、所在地等新字段&#10;- 更新了用户相关的 API 接口，支持新增的字段&#10;- 优化了用户服务中的部分方法，提高了代码可读性和性能&#10;- 修复了一些与用户相关的潜在安全问题" />
    <MESSAGE value="feat: 重构日志系统并初始化全局日志配置" />
    <MESSAGE value="feat(app): 添加 Excel 文件处理工具类&#10;&#10;- 实现了 Excel 文件验证、解析和模板生成功能- 支持榜单明细 Excel 文件的导入和导出&#10;- 提供了数据验证和错误处理机制" />
    <MESSAGE value="feat: 简化赞助商模型并添加用户个人资料字段&#10;&#10;- 移除赞助商模型中的冗余字段，只保留名称和logo_url&#10;- 在用户模型中添加地点、用户编号、性别和年龄字段&#10;- 新增文件上传API，支持Excel文件验证、解析和临时上传&#10;- 添加相关单元测试用例" />
    <MESSAGE value="feat: 支持 Excel 文件导入榜单明细&#10;&#10;- 新增创建和更新榜单时导入 Excel 数据的功能&#10;- 添加解析 Excel 文件的工具类 ExcelHandler&#10;- 修改数据库模型以支持榜单明细数据&#10;- 优化 API 接口，增加 Excel 文件上传和处理相关参数" />
    <MESSAGE value="feat: 启用系统配置和内容管理模块&#10;&#10;- 移除系统配置和服务模块的注释&#10;- 更新数据库会话类型从 AsyncSession 到 Session&#10;-修正部分导入语句和类型注解&#10;- 优化部分代码结构以适应同步数据库操作" />
    <MESSAGE value="refactor(api): 更新 API端点以使用异步数据库会话&#10;&#10;- 将 Session 替换为 AsyncSession&#10;- 使用 model_validate 替代 from_orm- 启用系统配置和内容管理路由" />
    <MESSAGE value="fix: 优化榜单更新接口并添加队伍名称字段&#10;&#10;- 移除 update_ranking 接口中的 ranking_id 参数，使用请求体中的 id&#10;- 在 RankingDetail 模型中添加 team_name 字段&#10;- 更新 RankingUpdate 模型，将 id 设为必填字段" />
    <option name="LAST_COMMIT_MESSAGE" value="fix: 优化榜单更新接口并添加队伍名称字段&#10;&#10;- 移除 update_ranking 接口中的 ranking_id 参数，使用请求体中的 id&#10;- 在 RankingDetail 模型中添加 team_name 字段&#10;- 更新 RankingUpdate 模型，将 id 设为必填字段" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>