"""Update ranking_details table to match current SQLAlchemy model

Revision ID: 004_update_ranking_details_to_current_model
Revises: 003_add_team_name_to_ranking_details
Create Date: 2024-12-19 18:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '004_update_ranking_details_to_current_model'
down_revision = '003_add_team_name_to_ranking_details'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """升级数据库：将ranking_details表结构更新为当前SQLAlchemy模型"""
    
    # 添加新字段
    op.add_column('ranking_details', sa.Column('participant_name', sa.String(length=100), nullable=False, comment='参与者姓名'))
    op.add_column('ranking_details', sa.Column('rank_range', sa.String(length=20), nullable=True, comment='排名范围'))
    op.add_column('ranking_details', sa.Column('notes', sa.Text(), nullable=True, comment='备注'))
    
    # 修改completion_seconds字段为可空
    op.alter_column('ranking_details', 'completion_seconds',
                   existing_type=sa.Integer(),
                   nullable=True,
                   comment='完成时间（秒）')
    
    # 删除旧字段
    op.drop_column('ranking_details', 'rank_start')
    op.drop_column('ranking_details', 'rank_end')
    op.drop_column('ranking_details', 'completion_time')
    op.drop_column('ranking_details', 'participant_count')
    op.drop_column('ranking_details', 'team_info')
    
    # 创建索引
    op.create_index('idx_ranking_details_completion_seconds', 'ranking_details', ['completion_seconds'])
    op.create_index('idx_ranking_details_created_at', 'ranking_details', ['created_at'])
    op.create_index('idx_ranking_details_ranking_id', 'ranking_details', ['ranking_id'])


def downgrade() -> None:
    """降级数据库：恢复原来的ranking_details表结构"""
    
    # 删除索引
    op.drop_index('idx_ranking_details_completion_seconds', table_name='ranking_details')
    op.drop_index('idx_ranking_details_created_at', table_name='ranking_details')
    op.drop_index('idx_ranking_details_ranking_id', table_name='ranking_details')
    
    # 删除新添加的字段
    op.drop_column('ranking_details', 'notes')
    op.drop_column('ranking_details', 'rank_range')
    op.drop_column('ranking_details', 'participant_name')
    
    # 恢复completion_seconds字段为非空
    op.alter_column('ranking_details', 'completion_seconds',
                   existing_type=sa.Integer(),
                   nullable=False,
                   comment='完成时间(总秒数)')
    
    # 恢复原来的字段
    op.add_column('ranking_details', sa.Column('rank_start', sa.Integer(), nullable=False, comment='排名开始'))
    op.add_column('ranking_details', sa.Column('rank_end', sa.Integer(), nullable=False, comment='排名结束'))
    op.add_column('ranking_details', sa.Column('completion_time', sa.Time(), nullable=False, comment='完成时间(分秒)'))
    op.add_column('ranking_details', sa.Column('participant_count', sa.Integer(), nullable=False, comment='当前时间区间参与人数'))
    op.add_column('ranking_details', sa.Column('team_info', sa.Text(), nullable=True, comment='队伍信息(JSON格式)'))
